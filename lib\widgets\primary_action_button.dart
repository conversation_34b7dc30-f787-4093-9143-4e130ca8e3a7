import 'package:flutter/material.dart';

/// A reusable primary action button widget that follows Upshift's design system.
/// This button provides consistent styling and behavior across the app.
class PrimaryActionButton extends StatelessWidget {
  /// The text to display on the button
  final String text;
  
  /// The text to display when the button is in loading state
  final String? loadingText;
  
  /// Callback function when the button is pressed
  final VoidCallback? onPressed;
  
  /// Whether the button is in a loading state
  final bool isLoading;
  
  /// Optional icon to display before the text
  final IconData? icon;
  
  /// Whether the button should take the full width of its parent
  final bool fullWidth;
  
  /// Custom padding for the button
  final EdgeInsetsGeometry? padding;

  const PrimaryActionButton({
    super.key,
    required this.text,
    this.loadingText,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.fullWidth = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveOnPressed = isLoading ? null : onPressed;
    final effectiveText = isLoading ? (loadingText ?? text) : text;
    
    Widget button;
    
    if (icon != null && !isLoading) {
      // Button with icon
      button = ElevatedButton.icon(
        onPressed: effectiveOnPressed,
        icon: Icon(icon),
        label: Text(effectiveText),
        style: _getButtonStyle(),
      );
    } else {
      // Button without icon or in loading state
      button = ElevatedButton(
        onPressed: effectiveOnPressed,
        style: _getButtonStyle(),
        child: isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 8),
                  Text(effectiveText),
                ],
              )
            : Text(effectiveText),
      );
    }
    
    if (fullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }
    
    return button;
  }
  
  ButtonStyle _getButtonStyle() {
    return ElevatedButton.styleFrom(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 12),
    );
  }
}
