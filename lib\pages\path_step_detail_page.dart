import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import '../theme/theme.dart';
import '../widgets/youtube_video_resource_widget.dart';
import '../widgets/generic_resource_widget.dart';
import '../widgets/chat_card.dart';
import '../widgets/primary_action_button.dart';
import 'persona_selection_page.dart';
import 'chat_page.dart';

class PathStepDetailPage extends StatefulWidget {
  final models.PathStep pathStep;
  final models.GuidedPath guidedPath;
  final models.UserPathProgress? userProgress;

  const PathStepDetailPage({
    super.key,
    required this.pathStep,
    required this.guidedPath,
    this.userProgress,
  });

  @override
  State<PathStepDetailPage> createState() => _PathStepDetailPageState();
}

class _PathStepDetailPageState extends State<PathStepDetailPage> {
  bool _isCompleting = false;
  models.User? _currentUser;
  models.Chat? _stepChat;
  models.SystemPersona? _stepChatPersona;
  bool _isLoadingChat = false;

  // Local copy of user progress that can be refreshed
  models.UserPathProgress? _currentUserProgress;

  bool get _isCompleted =>
      _currentUserProgress?.isStepCompleted(widget.pathStep.id!) ?? false;

  bool get _hasStepChat =>
      _currentUserProgress?.hasStepChatId(widget.pathStep.id!) ?? false;

  String? get _stepChatId =>
      _currentUserProgress?.getStepChatId(widget.pathStep.id!);
  bool get _isCurrentStep {
    if (widget.userProgress == null || widget.pathStep.id == null) return false;

    // Check if this step is completed
    final stepProgress = widget.userProgress!.getStepProgress(
      widget.pathStep.id!,
    );
    if (stepProgress?.isCompleted == true) return false;

    // If it's the first step and not completed, it's current
    if (widget.pathStep.stepNumber == 1) return true;

    // This is a simplified check - in a full implementation, you'd check if previous steps are completed
    return stepProgress != null && !stepProgress.isCompleted;
  }

  @override
  void initState() {
    super.initState();
    // Initialize local progress with widget data
    _currentUserProgress = widget.userProgress;
    _loadCurrentUser();
    _ensureStepProgressExists();
    _refreshUserProgress();
    // Load the step chat if it exists
    if (_hasStepChat) {
      _loadStepChat();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when the page becomes visible again
    // This handles the case where user returns from a completed chat
    _refreshUserProgress();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final user = await FirestoreService.getUser(currentUser.uid);
        setState(() {
          _currentUser = user;
        });
      }
    } catch (e) {
      // Handle error silently, user will just not have admin privileges
    }
  }

  /// Ensure PathStepProgress exists for this step
  Future<void> _ensureStepProgressExists() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null ||
          _currentUserProgress == null ||
          widget.pathStep.id == null) {
        return;
      }

      // Check if step progress already exists
      final existingProgress = _currentUserProgress!.getStepProgress(
        widget.pathStep.id!,
      );
      if (existingProgress != null) {
        return; // Already exists
      }

      // Create new PathStepProgress
      final newStepProgress = models.PathStepProgress(
        stepId: widget.pathStep.id!,
        isCompleted: false,
      );

      // Update UserPathProgress with the new step progress
      final updatedUserProgress = _currentUserProgress!.updateStepProgress(
        widget.pathStep.id!,
        newStepProgress,
      );

      // Save to Firestore
      await FirestoreService.createOrUpdateUserPathProgress(
        updatedUserProgress,
      );

      // Update local state
      setState(() {
        _currentUserProgress = updatedUserProgress;
      });
    } catch (e) {
      // Handle error silently
      debugPrint('Failed to ensure step progress exists: $e');
    }
  }

  /// Refreshes the user progress data from Firestore
  Future<void> _refreshUserProgress() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final updatedProgress = await FirestoreService.getUserPathProgress(
        currentUser.uid,
        widget.guidedPath.id!,
      );

      if (mounted && updatedProgress != null) {
        setState(() {
          _currentUserProgress = updatedProgress;
        });

        // Reload step chat if the chat ID has changed
        if (_hasStepChat && _stepChat == null) {
          _loadStepChat();
        }
      }
    } catch (e) {
      debugPrint('Error refreshing user progress: $e');
    }
  }

  /// Determines if the current user can access this step
  bool get _canAccessStep {
    // Admin users can access any step
    if (_currentUser?.isAdmin == true) {
      return true;
    }

    // If no progress exists, only the first step is accessible
    if (widget.userProgress == null) {
      return widget.pathStep.stepNumber == 1;
    }

    // If step is already completed, it's accessible
    if (_isCompleted) {
      return true;
    }

    // If it's the current step, it's accessible
    if (_isCurrentStep) {
      return true;
    }

    // Otherwise, it's not accessible
    return false;
  }

  Future<void> _completeStep() async {
    if (_isCompleted) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Update the legacy completion tracking
      await FirestoreService.completeUserPathStep(
        currentUser.uid,
        widget.guidedPath.id!,
        widget.pathStep.id!,
      );

      // Also update the new step progress tracking if we have userProgress
      if (_currentUserProgress != null) {
        final updatedProgress = _currentUserProgress!.markStepCompleted(
          widget.pathStep.id!,
        );
        await FirestoreService.createOrUpdateUserPathProgress(updatedProgress);

        // Update local state
        setState(() {
          _currentUserProgress = updatedProgress;
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Step ${widget.pathStep.stepNumber} completed!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // Return to path detail
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete step: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCompleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Step ${widget.pathStep.stepNumber}'),
        actions: [
          if (_isCompleted) Icon(AppIcons.completed, color: AppColors.success),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildContent(),
            if (widget.pathStep.reflectionPrompts?.isNotEmpty == true)
              _buildReflectionPrompts(),
            _buildChatSection(),
            _buildCompletionCriteria(),
            if (widget.pathStep.resources?.isNotEmpty == true)
              _buildResources(),
            const SizedBox(height: 100), // Space for bottom bar
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step indicator and path info
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor(),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  widget.guidedPath.category,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.pathStep.stepNumber} of ${widget.guidedPath.stepCount}',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Title
          Text(
            widget.pathStep.title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.pathStep.description, style: theme.textTheme.bodyLarge),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCompletionCriteria() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.task_alt, color: colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Completion Criteria',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.pathStep.completionCriteria,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildChatSection() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.chat_rounded, size: 20),
              const SizedBox(width: 8),
              Text(
                'Coach Session',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_hasStepChat)
            _buildExistingChatCard()
          else
            _buildStartChatButton(),
        ],
      ),
    );
  }

  Widget _buildStartChatButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Get personalized guidance for this step by chatting with an AI coach.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: 12),
        PrimaryActionButton(
          text: 'Start Chat',
          loadingText: 'Starting Chat...',
          onPressed: _startChat,
          isLoading: _isLoadingChat,
        ),
      ],
    );
  }

  Widget _buildExistingChatCard() {
    if (_stepChat == null) {
      // Load the chat if we have a chatId but haven't loaded the chat yet
      _loadStepChat();
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Continue your conversation with your AI coach for this step.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: 12),
        ChatCard(
          chat: _stepChat!,
          systemPersona: _stepChatPersona,
          onRefresh: () {
            // Refresh callback if needed
          },
        ),
      ],
    );
  }

  Widget _buildResources() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resources',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.resources!.map(
            (resource) => _buildResourceItem(resource),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceItem(models.ExternalResource resource) {
    // Check if this is a video resource with a YouTube URL
    if (resource.type == models.ExternalResourceType.video &&
        _isYouTubeUrl(resource.link)) {
      return YouTubeVideoResourceWidget(resource: resource);
    }

    // For all other resource types, use the generic resource widget
    return GenericResourceWidget(resource: resource);
  }

  /// Check if the given URL is a YouTube URL
  bool _isYouTubeUrl(String url) {
    try {
      final videoId = YoutubePlayerController.convertUrlToId(url);
      return videoId != null;
    } catch (e) {
      return false;
    }
  }

  Widget _buildReflectionPrompts() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bubble_chart, size: 20),
              const SizedBox(width: 8),
              Text(
                'Reflection Prompts',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.reflectionPrompts!.asMap().entries.map(
            (entry) => _buildReflectionPrompt(entry.key + 1, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildReflectionPrompt(int index, String prompt) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          width: 2,
          color: colorScheme.secondary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: colorScheme.secondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$index',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              prompt,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startChat() async {
    setState(() {
      _isLoadingChat = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Navigate to persona selection page and wait for result
      final selectedPersonaId = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) =>
              const PersonaSelectionPage(returnPersonaIdOnly: true),
        ),
      );

      if (selectedPersonaId == null) {
        // User cancelled persona selection
        return;
      }

      // Create new chat with selected persona and path step context
      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: selectedPersonaId,
        title: '${widget.guidedPath.name}: ${widget.pathStep.title}',
        pathStep: widget.pathStep,
      );

      // Update user path progress with the new chat ID
      if (_currentUserProgress != null) {
        final updatedProgress = _currentUserProgress!.setStepChatId(
          widget.pathStep.id!,
          chatId,
        );
        await FirestoreService.createOrUpdateUserPathProgress(updatedProgress);

        // Update local state
        setState(() {
          _currentUserProgress = updatedProgress;
        });
      }

      // Fetch the created chat object
      final chat = await FirestoreService.getChat(currentUser.uid, chatId);

      // Navigate to ChatPage with the chat object and path step context
      if (mounted && chat != null) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                ChatPage(chat: chat, pathStep: widget.pathStep),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load chat data'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start chat: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingChat = false;
        });
      }
    }
  }

  Future<void> _loadStepChat() async {
    final chatId = _stepChatId;
    if (chatId == null) return;

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final chat = await FirestoreService.getChat(currentUser.uid, chatId);
      if (chat != null) {
        final persona = await FirestoreService.getSystemPersona(
          chat.systemPersonaId,
        );

        if (mounted) {
          setState(() {
            _stepChat = chat;
            _stepChatPersona = persona;
          });
        }
      }
    } catch (e) {
      debugPrint('Failed to load step chat: $e');
    }
  }

  Widget _buildBottomBar() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(child: _buildActionButton()),
    );
  }

  Widget _buildActionButton() {
    if (_isCompleted) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: null,
          icon: const Icon(Icons.check_circle),
          label: const Text('Step Completed'),
          style: ElevatedButton.styleFrom(
            padding: AppDimensions.paddingVerticalM,
            backgroundColor: AppColors.success,
            foregroundColor: AppColors.onPrimary,
          ),
        ),
      );
    }

    if (!_canAccessStep) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Complete previous steps first'),
        ),
      );
    }

    // Only show "Mark as Complete" button if the step has an associated chat
    if (!_hasStepChat) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Start a chat to complete this step'),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isCompleting ? null : _completeStep,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isCompleting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('Completing...'),
                ],
              )
            : const Text('Mark as Complete'),
      ),
    );
  }

  Color _getCategoryColor() {
    return AppColors.getCategoryColor(widget.guidedPath.category);
  }
}
